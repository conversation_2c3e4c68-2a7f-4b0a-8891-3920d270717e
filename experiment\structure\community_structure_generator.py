#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
社区结构生成器
用于创建不同类型的社区结构，支持对比实验
"""

import json
import logging
import random
import math
import os
from typing import Dict, Any, List, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime

# 配置日志记录
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("CommunityStructureGenerator")


@dataclass
class CommunityStructureConfig:
    """社区结构配置类"""
    structure_type: str  # "type1" 或 "type2"
    num_users: int
    experiment_name: str
    description: str
    
    # 一类社区参数（知识分子主导型）
    intellectual_ratio: float = 0.1  # 知识分子比例
    intellectual_min_followers: int = 50  # 知识分子最少粉丝数
    intellectual_max_followers: int = 200  # 知识分子最多粉丝数
    regular_follow_mean_ratio: float = 0.1  # 普通群众粉丝数均值比例
    regular_follow_std_ratio: float = 0.05  # 普通群众粉丝数标准差比例
    regular_follow_max_ratio: float = 0.3   # 普通群众最大粉丝数比例
    
    # 二类社区参数（子社区型）
    num_subcommunities: int = 5  # 子社区数量
    subcommunity_size_min: int = 15  # 子社区最小规模
    subcommunity_size_max: int = 25  # 子社区最大规模
    moderator_ratio: float = 0.1  # 版主比例（每个子社区内）
    inter_community_connection_ratio: float = 0.05  # 子社区间连接比例
    intra_community_connection_ratio: float = 0.6   # 子社区内连接比例


class CommunityStructureGenerator:
    """社区结构生成器"""
    
    def __init__(self, config: CommunityStructureConfig):
        self.config = config
        self.users = {}
        self.follow_relationships = []
        
    def generate_users(self) -> Dict[str, Dict[str, Any]]:
        """生成用户"""
        logger.info(f"开始生成 {self.config.num_users} 个用户...")
        
        users = {}
        for i in range(1, self.config.num_users + 1):
            user_id = f"user_{i}"
            
            # 随机生成大五人格特质
            ocean = {
                "O": round(random.uniform(0.2, 0.9), 2),  # 开放性
                "C": round(random.uniform(0.2, 0.9), 2),  # 尽责性 
                "E": round(random.uniform(0.2, 0.9), 2),  # 外向性
                "A": round(random.uniform(0.2, 0.9), 2),  # 宜人性
                "N": round(random.uniform(0.2, 0.9), 2)   # 神经质
            }
            
            # 认知特质
            cognitive_traits = {
                "skepticism_score": round(random.uniform(0.2, 0.9), 2),
                "verification_threshold": round(random.uniform(0.2, 0.9), 2),
                "emotional_volatility": round(random.uniform(0.2, 0.9), 2),
                "is_intellectual": False,  # 将在结构生成时设置
                "subcommunity_id": None   # 将在结构生成时设置
            }
            
            users[user_id] = {
                "user_id": user_id,
                "ocean_personality": ocean,
                "cognitive_traits": cognitive_traits,
                "followed_posts": [],
                "followed_users": [],
                "followers": [],
                "posts": [],
                "comments": []
            }
            
        self.users = users
        logger.info(f"成功生成 {len(users)} 个用户")
        return users
    
    def generate_type1_structure(self) -> List[Tuple[str, str]]:
        """
        生成一类社区结构：知识分子主导型
        - 少量知识分子拥有大量粉丝
        - 普通群众粉丝数正态分布
        """
        logger.info("生成一类社区结构（知识分子主导型）...")
        
        user_ids = list(self.users.keys())
        num_intellectuals = max(1, int(len(user_ids) * self.config.intellectual_ratio))
        
        # 随机选择知识分子
        intellectual_ids = random.sample(user_ids, num_intellectuals)
        regular_user_ids = [uid for uid in user_ids if uid not in intellectual_ids]
        
        # 标记知识分子
        for uid in intellectual_ids:
            self.users[uid]["cognitive_traits"]["is_intellectual"] = True
            # 知识分子通常有更高的怀疑分数和验证阈值
            self.users[uid]["cognitive_traits"]["skepticism_score"] = round(random.uniform(0.6, 0.9), 2)
            self.users[uid]["cognitive_traits"]["verification_threshold"] = round(random.uniform(0.6, 0.9), 2)
            self.users[uid]["cognitive_traits"]["emotional_volatility"] = round(random.uniform(0.2, 0.5), 2)
        
        # 标记普通群众
        for uid in regular_user_ids:
            self.users[uid]["cognitive_traits"]["is_intellectual"] = False
            # 普通群众通常有较低的怀疑分数
            self.users[uid]["cognitive_traits"]["skepticism_score"] = round(random.uniform(0.2, 0.5), 2)
            self.users[uid]["cognitive_traits"]["verification_threshold"] = round(random.uniform(0.2, 0.5), 2)
            self.users[uid]["cognitive_traits"]["emotional_volatility"] = round(random.uniform(0.5, 0.9), 2)
        
        follow_relationships = []
        
        # 1. 普通群众关注知识分子（知识分子获得大量粉丝）
        for intellectual_id in intellectual_ids:
            num_followers = random.randint(
                self.config.intellectual_min_followers,
                min(self.config.intellectual_max_followers, len(regular_user_ids))
            )
            followers = random.sample(regular_user_ids, num_followers)
            
            for follower_id in followers:
                follow_relationships.append((follower_id, intellectual_id))
        
        # 2. 普通群众之间的关注关系（正态分布）
        total_users = len(regular_user_ids)
        mean_followers = total_users * self.config.regular_follow_mean_ratio
        std_followers = total_users * self.config.regular_follow_std_ratio
        max_followers = int(total_users * self.config.regular_follow_max_ratio)
        
        for user_id in regular_user_ids:
            # 使用正态分布生成粉丝数量
            follower_count = random.normalvariate(mean_followers, std_followers)
            follower_count = max(0, min(max_followers, int(round(follower_count))))
            
            if follower_count > 0:
                potential_followers = [uid for uid in regular_user_ids if uid != user_id]
                actual_follower_count = min(follower_count, len(potential_followers))
                
                if actual_follower_count > 0:
                    followers = random.sample(potential_followers, actual_follower_count)
                    for follower_id in followers:
                        follow_relationships.append((follower_id, user_id))
        
        # 3. 知识分子之间的相互关注
        for i, intellectual_id in enumerate(intellectual_ids):
            other_intellectuals = [uid for j, uid in enumerate(intellectual_ids) if i != j]
            if other_intellectuals:
                # 知识分子之间有较高的相互关注概率
                num_to_follow = max(1, int(len(other_intellectuals) * 0.7))
                followees = random.sample(other_intellectuals, min(num_to_follow, len(other_intellectuals)))
                
                for followee_id in followees:
                    follow_relationships.append((intellectual_id, followee_id))
        
        self.follow_relationships = follow_relationships
        logger.info(f"一类社区结构生成完成：{num_intellectuals} 个知识分子，{len(regular_user_ids)} 个普通群众，{len(follow_relationships)} 个关注关系")
        return follow_relationships

    def generate_type2_structure(self) -> List[Tuple[str, str]]:
        """
        生成二类社区结构：子社区型
        - 分为若干子社区
        - 每个子社区有版主（头领）
        - 子社区内联系密切，子社区间联系较少
        """
        logger.info("生成二类社区结构（子社区型）...")

        user_ids = list(self.users.keys())

        # 计算每个子社区的大小
        subcommunity_sizes = []
        remaining_users = len(user_ids)

        for i in range(self.config.num_subcommunities):
            if i == self.config.num_subcommunities - 1:
                # 最后一个子社区包含所有剩余用户
                size = remaining_users
            else:
                # 随机分配子社区大小
                max_size = min(self.config.subcommunity_size_max, remaining_users - (self.config.num_subcommunities - i - 1) * self.config.subcommunity_size_min)
                size = random.randint(self.config.subcommunity_size_min, max_size)
                remaining_users -= size

            subcommunity_sizes.append(size)

        # 分配用户到子社区
        random.shuffle(user_ids)
        subcommunities = {}
        user_index = 0

        for i, size in enumerate(subcommunity_sizes):
            subcommunity_id = f"subcommunity_{i+1}"
            subcommunity_users = user_ids[user_index:user_index + size]
            subcommunities[subcommunity_id] = subcommunity_users

            # 为用户标记子社区
            for user_id in subcommunity_users:
                self.users[user_id]["cognitive_traits"]["subcommunity_id"] = subcommunity_id

            user_index += size

        follow_relationships = []

        # 为每个子社区生成结构
        for subcommunity_id, subcommunity_users in subcommunities.items():
            logger.info(f"处理子社区 {subcommunity_id}，用户数：{len(subcommunity_users)}")

            # 选择版主（头领）
            num_moderators = max(1, int(len(subcommunity_users) * self.config.moderator_ratio))
            moderator_ids = random.sample(subcommunity_users, num_moderators)
            regular_members = [uid for uid in subcommunity_users if uid not in moderator_ids]

            # 标记版主
            for moderator_id in moderator_ids:
                self.users[moderator_id]["cognitive_traits"]["is_intellectual"] = True
                # 版主有更高的认知特质
                self.users[moderator_id]["cognitive_traits"]["skepticism_score"] = round(random.uniform(0.6, 0.9), 2)
                self.users[moderator_id]["cognitive_traits"]["verification_threshold"] = round(random.uniform(0.6, 0.9), 2)
                self.users[moderator_id]["cognitive_traits"]["emotional_volatility"] = round(random.uniform(0.2, 0.5), 2)

            # 标记普通成员
            for member_id in regular_members:
                self.users[member_id]["cognitive_traits"]["is_intellectual"] = False
                # 普通成员认知特质相似（体现子社区内的相似性）
                base_skepticism = random.uniform(0.3, 0.7)
                self.users[member_id]["cognitive_traits"]["skepticism_score"] = round(random.gauss(base_skepticism, 0.1), 2)
                self.users[member_id]["cognitive_traits"]["verification_threshold"] = round(random.gauss(base_skepticism, 0.1), 2)
                self.users[member_id]["cognitive_traits"]["emotional_volatility"] = round(random.uniform(0.4, 0.8), 2)

            # 子社区内的关注关系
            # 1. 普通成员关注版主
            for moderator_id in moderator_ids:
                # 大部分普通成员关注版主
                followers = random.sample(regular_members, int(len(regular_members) * 0.8))
                for follower_id in followers:
                    follow_relationships.append((follower_id, moderator_id))

            # 2. 子社区内成员之间的相互关注
            for user_id in subcommunity_users:
                other_members = [uid for uid in subcommunity_users if uid != user_id]
                if other_members:
                    # 高密度的内部连接
                    num_to_follow = int(len(other_members) * self.config.intra_community_connection_ratio)
                    if num_to_follow > 0:
                        followees = random.sample(other_members, min(num_to_follow, len(other_members)))
                        for followee_id in followees:
                            follow_relationships.append((user_id, followee_id))

        # 子社区间的连接（较少）
        subcommunity_list = list(subcommunities.keys())
        for i, subcommunity1 in enumerate(subcommunity_list):
            for j, subcommunity2 in enumerate(subcommunity_list):
                if i >= j:
                    continue

                users1 = subcommunities[subcommunity1]
                users2 = subcommunities[subcommunity2]

                # 少量跨社区连接
                num_connections = int(min(len(users1), len(users2)) * self.config.inter_community_connection_ratio)

                for _ in range(num_connections):
                    user1 = random.choice(users1)
                    user2 = random.choice(users2)
                    # 随机决定关注方向
                    if random.random() < 0.5:
                        follow_relationships.append((user1, user2))
                    else:
                        follow_relationships.append((user2, user1))

        self.follow_relationships = follow_relationships

        # 统计信息
        total_moderators = sum(1 for user in self.users.values()
                              if user["cognitive_traits"]["is_intellectual"])

        logger.info(f"二类社区结构生成完成：{self.config.num_subcommunities} 个子社区，{total_moderators} 个版主，{len(follow_relationships)} 个关注关系")
        return follow_relationships

    def generate_structure(self) -> Tuple[Dict[str, Dict[str, Any]], List[Tuple[str, str]]]:
        """
        根据配置生成相应的社区结构

        Returns:
            Tuple[用户字典, 关注关系列表]
        """
        # 首先生成用户
        users = self.generate_users()

        # 根据结构类型生成关注关系
        if self.config.structure_type == "type1":
            follow_relationships = self.generate_type1_structure()
        elif self.config.structure_type == "type2":
            follow_relationships = self.generate_type2_structure()
        else:
            raise ValueError(f"不支持的社区结构类型: {self.config.structure_type}")

        return users, follow_relationships

    def save_experiment_config(self, output_dir: str = "experiment/structure"):
        """
        保存实验配置到文件

        Args:
            output_dir: 输出目录
        """
        os.makedirs(output_dir, exist_ok=True)

        # 生成时间戳
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # 保存配置
        config_file = os.path.join(output_dir, f"{self.config.experiment_name}_{timestamp}_config.json")
        config_data = asdict(self.config)
        config_data["timestamp"] = timestamp
        config_data["total_users"] = len(self.users)
        config_data["total_relationships"] = len(self.follow_relationships)

        # 添加统计信息
        if self.config.structure_type == "type1":
            intellectuals = [uid for uid, user in self.users.items()
                           if user["cognitive_traits"]["is_intellectual"]]
            config_data["statistics"] = {
                "intellectuals_count": len(intellectuals),
                "regular_users_count": len(self.users) - len(intellectuals),
                "intellectual_ratio_actual": len(intellectuals) / len(self.users)
            }
        elif self.config.structure_type == "type2":
            subcommunities = {}
            moderators = []
            for uid, user in self.users.items():
                subcommunity_id = user["cognitive_traits"]["subcommunity_id"]
                if subcommunity_id not in subcommunities:
                    subcommunities[subcommunity_id] = []
                subcommunities[subcommunity_id].append(uid)

                if user["cognitive_traits"]["is_intellectual"]:
                    moderators.append(uid)

            config_data["statistics"] = {
                "subcommunities_count": len(subcommunities),
                "subcommunity_sizes": {k: len(v) for k, v in subcommunities.items()},
                "moderators_count": len(moderators),
                "moderator_ratio_actual": len(moderators) / len(self.users)
            }

        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config_data, f, ensure_ascii=False, indent=2)

        logger.info(f"实验配置已保存到: {config_file}")

        # 保存用户数据
        users_file = os.path.join(output_dir, f"{self.config.experiment_name}_{timestamp}_users.json")
        with open(users_file, 'w', encoding='utf-8') as f:
            json.dump(self.users, f, ensure_ascii=False, indent=2)

        logger.info(f"用户数据已保存到: {users_file}")

        # 保存关注关系
        relationships_file = os.path.join(output_dir, f"{self.config.experiment_name}_{timestamp}_relationships.json")
        relationships_data = [{"follower": rel[0], "followee": rel[1]} for rel in self.follow_relationships]
        with open(relationships_file, 'w', encoding='utf-8') as f:
            json.dump(relationships_data, f, ensure_ascii=False, indent=2)

        logger.info(f"关注关系已保存到: {relationships_file}")

        return config_file, users_file, relationships_file


def create_type1_community(num_users: int = 100, experiment_name: str = "type1_intellectual_dominant") -> CommunityStructureGenerator:
    """
    创建一类社区（知识分子主导型）

    Args:
        num_users: 用户总数
        experiment_name: 实验名称

    Returns:
        CommunityStructureGenerator实例
    """
    config = CommunityStructureConfig(
        structure_type="type1",
        num_users=num_users,
        experiment_name=experiment_name,
        description="知识分子主导型社区：少量知识分子拥有大量粉丝，普通群众粉丝数正态分布",
        intellectual_ratio=0.1,  # 10%的知识分子
        intellectual_min_followers=50,
        intellectual_max_followers=200,
        regular_follow_mean_ratio=0.1,
        regular_follow_std_ratio=0.05,
        regular_follow_max_ratio=0.3
    )

    generator = CommunityStructureGenerator(config)
    users, relationships = generator.generate_structure()

    logger.info(f"一类社区创建完成：{len(users)} 个用户，{len(relationships)} 个关注关系")
    return generator


def create_type2_community(num_users: int = 100, num_subcommunities: int = 5, experiment_name: str = "type2_subcommunity") -> CommunityStructureGenerator:
    """
    创建二类社区（子社区型）

    Args:
        num_users: 用户总数
        num_subcommunities: 子社区数量
        experiment_name: 实验名称

    Returns:
        CommunityStructureGenerator实例
    """
    config = CommunityStructureConfig(
        structure_type="type2",
        num_users=num_users,
        experiment_name=experiment_name,
        description="子社区型：分为若干子社区，每个子社区有版主，子社区间联系较少",
        num_subcommunities=num_subcommunities,
        subcommunity_size_min=max(10, num_users // num_subcommunities - 5),
        subcommunity_size_max=max(15, num_users // num_subcommunities + 5),
        moderator_ratio=0.15,  # 15%的版主
        inter_community_connection_ratio=0.05,  # 5%的跨社区连接
        intra_community_connection_ratio=0.6    # 60%的社区内连接
    )

    generator = CommunityStructureGenerator(config)
    users, relationships = generator.generate_structure()

    logger.info(f"二类社区创建完成：{len(users)} 个用户，{num_subcommunities} 个子社区，{len(relationships)} 个关注关系")
    return generator
